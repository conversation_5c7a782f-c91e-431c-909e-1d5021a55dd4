# 📋 Dokumentace - Správa zaměstnanců

## 🗂️ Kde se ukládají data zaměstnanců

### <PERSON><PERSON><PERSON><PERSON> datový soubor
**Soubor:** `zamestnanci.json`
**Umístění:** Kořenová složka projektu

### Struktura záznamu zaměstnance
```json
{
  "jmeno": "Jméno Příjmení",
  "pozice": "Název pozice",
  "oddeleni": "Název oddělení",
  "obrazek": "img/prijmeni.png",
  "telefon": "+*********** 789",
  "mobil": "+*********** 321",
  "email": "<EMAIL>",
  "teams": "https://teams.microsoft.com/l/chat/..."
}
```

## ➕ Jak přidat nového zaměstnance

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON> do JSON souboru
1. Otevřete soubor `zamestnanci.json`
2. Přidejte nový záznam na konec pole (před uzavírac<PERSON> `]`)
3. Dodržte správnou JSON syntaxi (č<PERSON>rky, uvozovky)

**Př<PERSON>lad:**
```json
{
  "jmeno": "Jan Novák",
  "pozice": "Specialista",
  "oddeleni": "ICT služby",
  "obrazek": "img/Novák.png",
  "telefon": "+*********** 890",
  "mobil": "+*********** 109",
  "email": "<EMAIL>",
  "teams": ""
}
```

### 2. Přidání fotografie
1. Uložte fotografii do složky `img/`
2. Doporučený formát: `Příjmení.png` nebo `Příjmení.jpg`
3. Doporučená velikost: 200x200px nebo větší (čtvercový formát)
4. Pokud fotografie není k dispozici, použije se automaticky `img/no-person-photo.png`

### 3. Přidání na mapu (volitelné)
Pokud chcete zaměstnance zobrazit na mapě kanceláří:

1. Otevřete soubor `mapa.html`
2. Najděte pole `markerPositions` (řádek cca 1233)
3. Přidejte nový záznam s pozicí na mapě:

```javascript
{
  "jmeno": "Jan Novák",
  "left": 1200,  // X pozice v pixelech
  "top": 150     // Y pozice v pixelech
}
```

**Jak určit pozici:**
1. Otevřete `mapa.html` v prohlížeči
2. Klikněte pravým tlačítkem na místo, kde chcete umístit zaměstnance
3. Vyberte "Prozkoumat prvek" (Inspect Element)
4. V konzoli napište: `console.log(event.clientX, event.clientY)` při kliknutí
5. Použijte získané souřadnice

## 🏢 Mapování oddělení na kanceláře

Aplikace automaticky přiřazuje kanceláře podle oddělení:

- **Představenstvo** → 4. patro
- **Financování a controlling** → 3. patro  
- **Účetnictví a daně** → 3. patro
- **ICT služby** → 2. patro
- **Kybernetická bezpečnost** → 2. patro
- **Bilance elektřiny** → 1. patro
- **Bilance plynu** → 1. patro
- **POZE** → 1. patro
- **Energetické trhy** → 1. patro
- **Kancelář PAS** → přízemí

## 🔧 Technické poznámky

### Podporované formáty obrázků
- PNG (doporučeno)
- JPG/JPEG
- WebP

### Povinná pole
- `jmeno` - vždy vyplňte
- `pozice` - vždy vyplňte
- `oddeleni` - vždy vyplňte
- `obrazek` - cesta k obrázku

### Volitelná pole
- `telefon` - zobrazí se "Není k dispozici" pokud prázdné
- `mobil` - zobrazí se "Není k dispozici" pokud prázdné  
- `email` - zobrazí se "Není k dispozici" pokud prázdné
- `teams` - pokud prázdné, nebude odkaz na Teams

### Více oddělení
Zaměstnanec může patřit do více oddělení oddělených čárkou:
```json
"oddeleni": "Účetnictví a daně, Představenstvo"
```

## 🚀 Po přidání zaměstnance

1. **Obnovte stránku** - změny se projeví okamžitě
2. **Zkontrolujte zobrazení** - ověřte, že se zaměstnanec zobrazuje správně
3. **Otestujte vyhledávání** - vyhledejte nového zaměstnance
4. **Zkontrolujte modal** - klikněte na kartu a ověřte údaje
5. **Otestujte mapu** - pokud jste přidali pozici, zkontrolujte zobrazení na mapě

## ⚠️ Časté chyby

### JSON syntaxe
- **Chybějící čárka** mezi záznamy
- **Chybějící uvozovky** kolem hodnot
- **Neuzavřené závorky** nebo uvozovky

### Obrázky
- **Špatná cesta** k obrázku
- **Chybějící soubor** obrázku
- **Nesprávný formát** souboru

### Mapa
- **Duplicitní jména** v markerPositions
- **Špatné souřadnice** (mimo mapu)
- **Chybějící záznam** v JSON ale existující na mapě

## 📞 Kontakt pro podporu

V případě problémů kontaktujte správce aplikace nebo IT oddělení.
