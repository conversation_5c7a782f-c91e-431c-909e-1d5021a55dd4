// Globální proměnné
let employees = [];
let filteredEmployees = [];

// Načtení z<PERSON>ů z JSON souboru
async function loadEmployees() {
    try {
        const response = await fetch('zamestnanci.json');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        employees = data;
        filteredEmployees = [...employees];

        // Seřadit data před generováním HTML
        filteredEmployees.sort((a, b) => {
            const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
            const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
            return nameA.localeCompare(nameB, 'cs');
        });

        generateDepartmentFilters();
        generateEmployeeHTML();
        updateEmployeeNumbers();
        initializeSearchPlaceholder();
    } catch (error) {
        console.error('Chyba při načítání zaměstnanců:', error);

        // Zobrazit uživateli chybovou zprávu
        showErrorMessage(`Nepodařilo se načíst data zaměstnanců: ${error.message}`);
    }
}

// Generování filtrů podle oddělení
function generateDepartmentFilters() {
    const departmentFiltersContainer = document.getElementById('department-filters');
    if (!departmentFiltersContainer) {
        console.error('Element #department-filters nenalezen');
        return;
    }

    // Získat unikátní oddělení (rozdělit více oddělení oddělených čárkou)
    const allDepartments = [];
    employees.forEach(emp => {
        const depts = emp.oddeleni.split(',').map(d => d.trim());
        allDepartments.push(...depts);
    });
    const departments = [...new Set(allDepartments)].sort();

    // Vymazat existující filtry
    departmentFiltersContainer.innerHTML = '';

    // Přidat filtr "Všechna oddělení" s počtem
    const allButton = document.createElement('button');
    allButton.className = 'filter-btn all active';
    allButton.setAttribute('data-filter', 'all');
    allButton.innerHTML = `<i class="fas fa-users"></i> Všechna oddělení <span class="count">(${employees.length})</span>`;
    departmentFiltersContainer.appendChild(allButton);



    // Přidat filtry pro jednotlivá oddělení s počty
    departments.forEach(department => {
        const button = document.createElement('button');
        button.className = 'filter-btn';
        button.setAttribute('data-filter', department);

        // Spočítat zaměstnance v oddělení (včetně těch s více odděleními)
        const count = employees.filter(emp => {
            const depts = emp.oddeleni.split(',').map(d => d.trim());
            return depts.includes(department);
        }).length;

        // Přidat ikonu podle oddělení
        let icon = 'fas fa-building'; // výchozí ikona
        if (department.includes('elektřin')) icon = 'fas fa-bolt';
        else if (department.includes('plyn')) icon = 'fas fa-fire';
        else if (department.includes('POZE')) icon = 'fas fa-leaf';
        else if (department.includes('trhy')) icon = 'fas fa-chart-line';
        else if (department.includes('ICT')) icon = 'fas fa-laptop-code';
        else if (department.includes('Financování')) icon = 'fas fa-calculator';
        else if (department.includes('PAS')) icon = 'fas fa-briefcase';
        else if (department.includes('Záruky')) icon = 'fas fa-certificate';
        else if (department.includes('Veřejné')) icon = 'fas fa-gavel';
        else if (department.includes('Účetnictví')) icon = 'fas fa-book';
        else if (department.includes('Smluvní')) icon = 'fas fa-file-contract';
        else if (department.includes('Správa')) icon = 'fas fa-building';
        else if (department.includes('Kybernetická')) icon = 'fas fa-shield-alt';
        else if (department.includes('Rozvoj')) icon = 'fas fa-chart-bar';
        else if (department.includes('Compliance')) icon = 'fas fa-balance-scale';
        else if (department.includes('Představenstvo')) icon = 'fas fa-users';

        button.innerHTML = `<i class="${icon}"></i> ${department} <span class="count">(${count})</span>`;
        departmentFiltersContainer.appendChild(button);
    });

    // Přidat event listenery pro filtry
    setupFilterEventListeners();
}

// Generování HTML pro zaměstnance podle struktury WEB 2
function generateEmployeeHTML() {
    const employeeGrid = document.getElementById('employee-grid');
    if (!employeeGrid) {
        console.error('Element #employee-grid nenalezen');
        return;
    }

    // Zobrazit loading
    employeeGrid.innerHTML = '<div class="loading-container"><div class="loading-spinner"></div><p>Načítání zaměstnanců...</p></div>';

    // Simulovat krátké načítání pro lepší UX
    setTimeout(() => {
        employeeGrid.innerHTML = '';

        if (filteredEmployees.length === 0) {
            employeeGrid.innerHTML = '<div class="no-results"><i class="fas fa-search"></i><p>Žádní zaměstnanci k zobrazení</p><p class="hint">Zkuste změnit filtr nebo vyhledávací dotaz</p></div>';
            return;
        }

        renderEmployees();
    }, 200);
}

function renderEmployees() {
    const employeeGrid = document.getElementById('employee-grid');

    // Generovat HTML pro každého zaměstnance podle struktury WEB 2
    filteredEmployees.forEach((employee, index) => {
        const employeeDiv = document.createElement('div');
        employeeDiv.className = 'employee';
        employeeDiv.setAttribute('data-departments', employee.oddeleni);
        employeeDiv.setAttribute('data-number', index + 1);

        // Rozdělit jméno na křestní jméno a příjmení
        const nameParts = employee.jmeno.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        employeeDiv.innerHTML = `
            <img src="${employee.obrazek}" alt="${employee.jmeno}" onerror="this.src='img/no-person-photo.png'">
            <div class="employee_name">
                <div class="first-name">${firstName}</div>
                <div class="last-name">${lastName}</div>
            </div>
            <p class="position">${employee.pozice}</p>
            <p class="job_type">${employee.oddeleni}</p>



            <div class="click-info">
                <i class="fas fa-hand-pointer"></i>
                <span>Více informací zobrazíte kliknutím</span>
            </div>
        `;

        // Přidat click event pro modal
        employeeDiv.addEventListener('click', () => {
            openModal(employee);
        });

        employeeGrid.appendChild(employeeDiv);

        // Přidat animaci pro postupné zobrazení
        setTimeout(() => {
            employeeDiv.style.opacity = '0';
            employeeDiv.style.transform = 'translateY(20px)';
            employeeDiv.style.transition = 'all 0.4s ease';

            setTimeout(() => {
                employeeDiv.style.opacity = '1';
                employeeDiv.style.transform = 'translateY(0)';
            }, index * 50); // Postupné zobrazení s 50ms zpožděním
        }, 0);
    });

    // Přidat spinning efekt na obrázky
    addSpinEffect();
}



// Nastavení event listenerů pro filtry
function setupFilterEventListeners() {
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Odstranit active třídu ze všech tlačítek
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Přidat active třídu k aktuálnímu tlačítku
            button.classList.add('active');

            // Získat filtr
            const filter = button.getAttribute('data-filter');

            // Filtrovat zaměstnance
            if (filter === 'all') {
                filteredEmployees = [...employees];

            } else {
                filteredEmployees = employees.filter(emp => {
                    const depts = emp.oddeleni.split(',').map(d => d.trim());
                    return depts.includes(filter);
                });
            }

            // Seřadit a zobrazit
            filteredEmployees.sort((a, b) => {
                const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
                const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
                return nameA.localeCompare(nameB, 'cs');
            });

            generateEmployeeHTML();
            updateEmployeeNumbers();
        });
    });
}



// Otevření modalu s daty zaměstnance
function openModal(employeeData) {
    const modal = document.getElementById('employeeModal');
    const modalImage = document.getElementById('modalImage');
    const modalName = document.getElementById('modalName');
    const modalPosition = document.getElementById('modalPosition');
    const modalDepartment = document.getElementById('modalDepartment');
    const modalOffice = document.getElementById('modalOffice');
    const modalDescription = document.getElementById('modalDescription');
    const modalPhone = document.getElementById('modalPhone');
    const modalMobile = document.getElementById('modalMobile');
    const modalEmail = document.getElementById('modalEmail');
    const modalTeams = document.getElementById('modalTeams');

    // Nastavit obrázek s fallback
    modalImage.src = employeeData.obrazek;
    modalImage.alt = employeeData.jmeno;
    modalImage.onerror = function() {
        this.src = 'img/no-person-photo.png';
    };

    // Nastavit jméno s badge pro vedoucí
    const firstName = employeeData.jmeno.split(' ')[0];
    const isManager = employeeData.pozice.toLowerCase().includes('vedoucí') ||
                     employeeData.pozice.toLowerCase().includes('předseda') ||
                     employeeData.pozice.toLowerCase().includes('místopředseda');

    modalName.innerHTML = employeeData.jmeno + (isManager ? '<span class="modal-badge">Vedení</span>' : '');

    // Nastavit pozici s emoji
    modalPosition.textContent = `Pracovní pozice: ${employeeData.pozice}`;

    // Nastavit oddělení
    modalDepartment.textContent = `Oddělení: ${employeeData.oddeleni}`;

    // Inteligentní určení kanceláře podle oddělení
    const office = getOfficeByDepartment(employeeData.oddeleni);
    const mapUrl = `mapa.html?employee=${encodeURIComponent(employeeData.jmeno)}&highlight=true`;
    modalOffice.innerHTML = `Kancelář: ${office} <a href="${mapUrl}" target="_blank" class="modal-action-btn map-btn" style="margin-left: 10px; padding: 6px 12px; font-size: 12px;"><i class="fas fa-map-marker-alt"></i> Zobrazit na mapě</a>`;

    // Inteligentní popis podle pozice
    const description = generateSmartDescription(employeeData);
    modalDescription.textContent = description;

    // Kontaktní informace s lepším formátováním
    modalPhone.textContent = formatPhoneNumber(employeeData.telefon) || 'Není k dispozici';
    modalMobile.textContent = formatPhoneNumber(employeeData.mobil) || 'Není k dispozici';
    modalEmail.innerHTML = employeeData.email ?
        `<a href="mailto:${employeeData.email}" style="color: #00808f; text-decoration: none;">${employeeData.email}</a>` :
        'Není k dispozici';

    // Teams link s lepším stylingem
    if (employeeData.teams) {
        modalTeams.innerHTML = `
            <div class="modal-actions">
                <a href="${employeeData.teams}" target="_blank" class="modal-action-btn">
                    <i class="fab fa-microsoft"></i> Kontaktovat přes Teams
                </a>
                <a href="mailto:${employeeData.email || ''}" class="modal-action-btn" ${!employeeData.email ? 'style="opacity: 0.5; pointer-events: none;"' : ''}>
                    <i class="fas fa-envelope"></i> Poslat email
                </a>
            </div>
        `;
    } else {
        modalTeams.innerHTML = `
            <div class="modal-actions">
                <span style="color: #666; font-style: italic;">Teams kontakt není k dispozici</span>
                <a href="mailto:${employeeData.email || ''}" class="modal-action-btn" ${!employeeData.email ? 'style="opacity: 0.5; pointer-events: none;"' : ''}>
                    <i class="fas fa-envelope"></i> Poslat email
                </a>
            </div>
        `;
    }

    modal.style.display = 'block';
}

// Pomocné funkce
function getOfficeByDepartment(department) {
    const officeMap = {
        'Představenstvo': 'Praha - Karolinská 661/4, 4. patro',
        'Financování a controlling': 'Praha - Karolinská 661/4, 3. patro',
        'Účetnictví a daně': 'Praha - Karolinská 661/4, 3. patro',
        'ICT služby': 'Praha - Karolinská 661/4, 2. patro',
        'Kybernetická bezpečnost a ochrana dat': 'Praha - Karolinská 661/4, 2. patro',
        'Bilance elektřiny': 'Praha - Karolinská 661/4, 1. patro',
        'Bilance plynu': 'Praha - Karolinská 661/4, 1. patro',
        'POZE': 'Praha - Karolinská 661/4, 1. patro',
        'Energetické trhy': 'Praha - Karolinská 661/4, 1. patro',
        'Kancelář PAS': 'Praha - Karolinská 661/4, přízemí'
    };

    // Najít nejlepší shodu
    for (const [dept, office] of Object.entries(officeMap)) {
        if (department.includes(dept)) {
            return office;
        }
    }

    return 'Praha - Karolinská 661/4';
}

function generateSmartDescription(employee) {
    const position = employee.pozice.toLowerCase();
    const department = employee.oddeleni.toLowerCase();
    const firstName = employee.jmeno.split(' ')[0];

    if (position.includes('vedoucí')) {
        return `${firstName} vede tým oddělení ${employee.oddeleni} a zodpovídá za strategické řízení a koordinaci týmu.`;
    } else if (position.includes('předseda')) {
        return `${firstName} zastává pozici předsedy představenstva a řídí strategické směřování společnosti.`;
    } else if (position.includes('místopředseda')) {
        return `${firstName} podporuje předsedu představenstva v řízení společnosti a zastupuje ho v jeho nepřítomnosti.`;
    } else if (department.includes('ict')) {
        return `${firstName} se stará o IT infrastrukturu a technologické řešení ve společnosti.`;
    } else if (department.includes('účetnictví')) {
        return `${firstName} zajišťuje účetní agendu a finanční reporting společnosti.`;
    } else if (department.includes('bilance')) {
        return `${firstName} se specializuje na bilancování energií a zajišťuje přesnost energetických toků.`;
    } else {
        return `${firstName} pracuje jako ${employee.pozice} v oddělení ${employee.oddeleni} a přispívá k úspěchu týmu.`;
    }
}

function formatPhoneNumber(phone) {
    if (!phone) return null;
    // Základní formátování českých čísel
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 9) {
        return `+420 ${cleaned.substring(0,3)} ${cleaned.substring(3,6)} ${cleaned.substring(6)}`;
    }
    return phone;
}

// Filtrování zaměstnanců podle oddělení
function filterEmployees(department) {
    if (department === 'all') {
        filteredEmployees = [...employees];
    } else {
        filteredEmployees = employees.filter(employee =>
            employee.oddeleni.includes(department)
        );
    }

    // Seřadit před zobrazením
    filteredEmployees.sort((a, b) => {
        const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
        const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
        return nameA.localeCompare(nameB, 'cs');
    });

    generateEmployeeHTML();
    updateEmployeeNumbers();
}

// Vyhledávání zaměstnanců
const displayEmployees = (values) => {
    const normalizedValues = removeDiacritics(values.toUpperCase());
    const activeFilter = document.querySelector('.filter-btn.active').getAttribute('data-filter');

    // Filtrovat zaměstnance podle vyhledávání a aktivního filtru
    filteredEmployees = employees.filter(employee => {
        const name = removeDiacritics(employee.jmeno.toUpperCase());
        const position = removeDiacritics(employee.pozice.toUpperCase());
        const department = removeDiacritics(employee.oddeleni.toUpperCase());

        const matchesSearch = name.includes(normalizedValues) || 
                            position.includes(normalizedValues) || 
                            department.includes(normalizedValues);
        const matchesFilter = activeFilter === 'all' || employee.oddeleni.includes(activeFilter);

        return matchesSearch && matchesFilter;
    });

    // Seřadit před zobrazením
    filteredEmployees.sort((a, b) => {
        const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
        const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
        return nameA.localeCompare(nameB, 'cs');
    });

    generateEmployeeHTML();
    updateEmployeeNumbers();
};



// Aktualizace počtu zaměstnanců
function updateEmployeeNumbers() {
    const filterHeader = document.querySelector('.filter-header');
    const count = filteredEmployees.length;

    if (!filterHeader) {
        return;
    }

    let countText = '';
    if (count === 0) {
        countText = 'Žádní zaměstnanci nenalezeni';
    } else if (count === 1) {
        countText = 'Zobrazen 1 zaměstnanec';
    } else if (count <= 4) {
        countText = `Zobrazeni ${count} zaměstnanci`;
    } else {
        countText = `Zobrazeno ${count} zaměstnanců`;
    }

    filterHeader.innerHTML = `
        <div class="employee-count-container">
            <i class="fas fa-users"></i>
            <h2>${countText}</h2>
        </div>
        <a href="mapa.html" target="_blank" class="map-link-container">
            <i class="fas fa-map-marked-alt"></i>
            <h2>Mapa rozmístění pracovišť</h2>
        </a>
    `;
}

// Funkce pro odstranění diakritiky
function removeDiacritics(str) {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}

// Přidání spinning efektu na obrázky
function addSpinEffect() {
    const employeeImages = document.querySelectorAll('.employee img');
    employeeImages.forEach(img => {
        img.addEventListener('mouseenter', function() {
            if (!this.classList.contains('spun')) {
                this.classList.add('spin');
                this.addEventListener('animationend', function() {
                    this.classList.remove('spin');
                    this.classList.add('spun');
                }, { once: true });
            }
        });
    });
}

// Inicializace placeholder animace pro vyhledávání
function initializeSearchPlaceholder() {
    const searchInput = document.getElementById('searchEmployee');
    const placeholders = [
        "Vyhledat zaměstnance...",
        "Zadejte jméno nebo příjmení...",
        "Hledat podle pozice...",
        "Najít kolegu z oddělení...",
        "Například: Novák, vedoucí..."
    ];

    let currentPlaceholderIndex = 0;
    let placeholderIndex = 0;
    let isDeleting = false;

    function animatePlaceholder() {
        const currentText = placeholders[currentPlaceholderIndex];

        if (!isDeleting && placeholderIndex <= currentText.length) {
            searchInput.placeholder = currentText.substring(0, placeholderIndex);
            placeholderIndex++;
            setTimeout(animatePlaceholder, 80);
        } else if (isDeleting && placeholderIndex >= 0) {
            searchInput.placeholder = currentText.substring(0, placeholderIndex);
            placeholderIndex--;
            setTimeout(animatePlaceholder, 40);
        } else if (!isDeleting && placeholderIndex > currentText.length) {
            setTimeout(() => {
                isDeleting = true;
                animatePlaceholder();
            }, 2000);
        } else if (isDeleting && placeholderIndex < 0) {
            isDeleting = false;
            currentPlaceholderIndex = (currentPlaceholderIndex + 1) % placeholders.length;
            setTimeout(animatePlaceholder, 500);
        }
    }

    animatePlaceholder();

    searchInput.addEventListener('focus', () => {
        searchInput.placeholder = 'Začněte psát...';
        searchInput.style.backgroundColor = '#fff';
        searchInput.style.borderColor = '#00add0';
    });

    searchInput.addEventListener('blur', () => {
        if (searchInput.value === '') {
            placeholderIndex = 0;
            isDeleting = false;
            currentPlaceholderIndex = 0;
            setTimeout(animatePlaceholder, 500);
        }
        searchInput.style.backgroundColor = '';
        searchInput.style.borderColor = '';
    });
}

// Event listenery pro vyhledávání
const searchInput = document.getElementById('searchEmployee');
const searchForm = document.querySelector('.search_form');
const searchBtn = document.getElementById('searchBtn');

searchInput.addEventListener('input', (e) => displayEmployees(e.target.value));

searchForm.addEventListener('submit', (e) => {
    e.preventDefault();
    displayEmployees(searchInput.value);
});

searchBtn.addEventListener('click', (e) => {
    e.preventDefault();
    displayEmployees(searchInput.value);
});

// Event listenery pro filter tlačítka
const filterButtons = document.querySelectorAll('.filter-btn');

filterButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Odstranit aktivní třídu ze všech tlačítek
        filterButtons.forEach(btn => btn.classList.remove('active'));
        
        // Přidat aktivní třídu na kliknuté tlačítko
        button.classList.add('active');
        
        // Získat filtr a aplikovat ho
        const department = button.getAttribute('data-filter');
        filterEmployees(department);
    });
});

// Modal funkcionalita
const modal = document.getElementById('employeeModal');
const closeBtn = document.querySelector('.close');

function closeModal() {
    modal.classList.add('closing');
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('closing');
    }, 300);
}

closeBtn.onclick = closeModal;

window.onclick = (event) => {
    if (event.target == modal) {
        closeModal();
    }
};

// Přepínač témat
const toggleSwitch = document.querySelector('.theme-switch input[type="checkbox"]');
const currentTheme = localStorage.getItem('theme');

if (currentTheme) {
    document.documentElement.setAttribute('data-theme', currentTheme);
  
    if (currentTheme === 'dark') {
        toggleSwitch.checked = true;
        document.body.classList.add('dark-mode');
    }
}

function switchTheme(e) {
    if (e.target.checked) {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
        localStorage.setItem('theme', 'dark');
    }
    else {
        document.documentElement.setAttribute('data-theme', 'light');
        document.body.classList.remove('dark-mode');
        localStorage.setItem('theme', 'light');
    }    
}

toggleSwitch.addEventListener('change', switchTheme, false);

// Funkce pro zobrazení chybové zprávy
function showErrorMessage(message) {
    const employeeListContainer = document.querySelector('.employee_listing');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        background: #fee;
        border: 1px solid #fcc;
        color: #c33;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        text-align: center;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <p>${message}</p>
    `;

    // Přidat za filtry
    const departmentFilters = employeeListContainer.querySelector('.department-filters');
    if (departmentFilters) {
        departmentFilters.insertAdjacentElement('afterend', errorDiv);
    } else {
        employeeListContainer.appendChild(errorDiv);
    }
}



// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    loadEmployees();
});