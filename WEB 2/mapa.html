<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> kanceláře</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f7f7f7;
      margin: 0;
      padding: 0;
    }
    .main-flex {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100vw;
      max-width: none;
      margin: 2rem 0 2rem 0;
      gap: 0;
    }
    .side-panel {
      background: rgba(255,255,255,0.92);
      box-shadow: 0 12px 48px 0 rgba(0,0,0,0.18), 0 2px 8px 0 rgba(0,123,255,0.08);
      border-radius: 22px;
      border: 2.5px solid #e3e8f0;
      margin-left: 0;
      margin-right: 0;
      padding: 2.5rem 1.5rem 2.5rem 2.2rem;
      min-width: 290px;
      max-width: 440px;
      transition: box-shadow 0.25s, background 0.25s;
      backdrop-filter: blur(14px);
      -webkit-backdrop-filter: blur(14px);
      position: sticky;
      top: 32px;
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 1.2rem;
      height: 900px;
      overflow-y: auto;
    }
    .side-panel h2 {
      font-size: 1.38rem;
      font-weight: 900;
      letter-spacing: 0.01em;
      margin-bottom: 1.2rem;
      background: none;
      position: sticky;
      top: 0;
      z-index: 2;
      padding-bottom: 0.7rem;
      background: rgba(255,255,255,0.92);
    }
    .search-box {
      margin-bottom: 1.3rem;
      border-radius: 10px;
      border: 2px solid #d0d0d0;
      padding: 14px 20px;
      font-size: 1.13rem;
      background: #f7f7fa;
      transition: border 0.2s;
      position: sticky;
      top: 54px;
      z-index: 3;
    }
    .search-box:focus {
      border: 2px solid #007bff;
      outline: none;
      background: #fff;
    }
    .employee-list {
      gap: 1.1rem;
      padding-top: 0.5rem;
      scrollbar-width: thin;
      scrollbar-color: #b3c0d1 #f7f7fa;
      flex: 1 1 auto;
      overflow-y: auto;
      max-height: 700px;
    }
    .employee-list::-webkit-scrollbar {
      width: 12px;
      background: #f7f7fa;
    }
    .employee-list::-webkit-scrollbar-thumb {
      background: #b3c0d1;
      border-radius: 8px;
    }
    .employee-list li {
      display: flex;
      align-items: center;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(0,123,255,0.07);
      padding: 14px 18px 14px 14px;
      margin-bottom: 0;
      font-size: 1.13rem;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid transparent;
      transition: box-shadow 0.18s, background 0.18s, transform 0.18s, border 0.18s;
      min-height: 56px;
      position: relative;
    }
    .employee-list li:hover, .employee-list li.active {
      background: linear-gradient(90deg, #e3f0ff 0%, #f7faff 100%);
      box-shadow: 0 4px 18px rgba(0,123,255,0.13);
      border: 2px solid #007bff33;
      transform: translateY(-2px) scale(1.03);
    }
    .employee-list li:focus {
      outline: 2px solid #007bff;
    }
    .avatar-img {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 18px;
      border: 2.5px solid #e3e8f0;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.09);
      vertical-align: middle;
      flex-shrink: 0;
    }
    .employee-list .emp-name {
      font-weight: 800;
      font-size: 1.13rem;
      color: #222;
      margin-right: auto;
      letter-spacing: 0.01em;
      display: block;
      line-height: 1.2;
    }
    .find-btn {
      background: #eaf3ff;
      color: #007bff;
      border: none;
      border-radius: 50px;
      padding: 8px 18px 8px 14px;
      font-size: 1.04rem;
      margin-left: 18px;
      cursor: pointer;
      transition: background 0.18s, color 0.18s, border 0.18s, box-shadow 0.18s;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 7px;
      box-shadow: 0 1px 4px rgba(0,123,255,0.07);
    }
    .find-btn:hover, .find-btn:focus {
      background: #007bff;
      color: #fff;
      box-shadow: 0 2px 8px rgba(0,123,255,0.13);
    }
    .find-btn .fa-search {
      font-size: 1.1em;
      margin-right: 2px;
    }
    .mapa-wrapper {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      background: transparent;
      min-width: 0;
      margin-left: 64px;
    }
    .office-map-container {
      position: relative;
      display: inline-block;
      background: transparent;
      box-shadow: 0 4px 24px rgba(0,0,0,0.08);
      border-radius: 0 12px 12px 0;
      border: none;
      padding: 0;
      margin: 0;
      max-width: 1920px;
      width: auto;
    }
    .office-map-container img {
      display: block;
      width: auto;
      max-width: 1920px;
      height: auto;
      -webkit-user-select: none;
      user-select: none;
      pointer-events: auto;
      background: transparent;
      border-radius: 0 12px 12px 0;
    }
    .marker {
      width: 36px;
      height: 36px;
      background: transparent;
      border: none;
      border-radius: 50%;
      position: absolute;
      cursor: grab;
      transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
      box-shadow: none;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      user-select: none;
      -webkit-user-select: none;
      padding: 0;
    }
    .marker:active {
      cursor: grabbing;
    }
    .marker.active {
      box-shadow: none;
      background: transparent;
      border: none;
    }
    .marker.added {
      animation: marker-pop 0.5s;
    }
    @keyframes marker-pop {
      0% { transform: scale(0.5); opacity: 0.2; }
      60% { transform: scale(1.3); opacity: 1; }
      100% { transform: scale(1); opacity: 1; }
    }
    .marker .tooltip {
      min-width: 120px;
      text-align: center;
    }
    .marker:hover .tooltip, .marker.active .tooltip {
      opacity: 1;
      pointer-events: auto;
    }
    .tooltip {
      position: absolute;
      top: -44px;
      left: 50%;
      transform: translateX(-50%);
      background: #222;
      color: #fff;
      padding: 7px 16px;
      border-radius: 8px;
      font-size: 16px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s;
      z-index: 10;
      box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    .error-message {
      color: #c62828;
      background: #fff3f3;
      border: 1px solid #ffcdd2;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      margin: 2rem auto;
      max-width: 600px;
      font-size: 1.2rem;
      display: none;
    }
    .mapping-hint {
      text-align: center;
      margin-bottom: 1rem;
      color: #444;
      font-size: 1.1rem;
      background: #fffbe7;
      border-radius: 8px;
      padding: 10px 20px;
      max-width: 900px;
      margin-left: auto;
      margin-right: auto;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    .mapping-dialog {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      padding: 24px 32px;
      z-index: 10000;
      min-width: 320px;
      max-width: 90vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    .mapping-dialog label {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
    .mapping-dialog select {
      font-size: 1.1rem;
      padding: 6px 12px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 100%;
    }
    .mapping-dialog button {
      margin-top: 1rem;
      padding: 8px 18px;
      border-radius: 6px;
      border: none;
      background: #007bff;
      color: #fff;
      font-size: 1.1rem;
      cursor: pointer;
      transition: background 0.2s;
    }
    .mapping-dialog button:hover {
      background: #0056b3;
    }
    @media (max-width: 1200px) {
      .main-flex {
        flex-direction: column;
        align-items: stretch;
      }
      .side-panel {
        border-radius: 12px 12px 0 0;
        height: 300px;
        max-width: 100vw;
        min-width: 0;
        width: 100vw;
        margin-bottom: 0;
      }
      .mapa-wrapper {
        justify-content: center;
      }
      .office-map-container {
        border-radius: 0 0 12px 12px;
      }
      .office-map-container img {
        border-radius: 0 0 12px 12px;
      }
    }
    .side-panel.collapsed {
      width: 38px !important;
      min-width: 38px !important;
      max-width: 38px !important;
      padding: 0 !important;
      overflow: hidden !important;
      transition: width 0.3s, min-width 0.3s, max-width 0.3s, padding 0.3s;
    }
    .side-panel.collapsed h2,
    .side-panel.collapsed .search-box,
    .side-panel.collapsed .employee-list,
    .side-panel.collapsed .find-btn,
    .side-panel.collapsed span {
      display: none !important;
    }
    .side-panel.collapsed #collapse-btn {
      left: 4px !important;
      right: auto !important;
      top: 8px !important;
    }
    .marker-avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: none;
      box-shadow: none;
      background: none;
      display: block;
    }
    .marker:hover, .marker.active {
      box-shadow: none;
      background: transparent;
      border: none;
    }
  </style>
</head>
<body>
  <h1 style="text-align:center; margin-top:2rem; font-size:2.3rem; font-weight: bold;">Mapa kanceláře</h1>
  <div class="main-flex">
    <aside class="side-panel">
      <button id="collapse-btn" onclick="togglePanel()" style="position:absolute;top:18px;right:18px;z-index:20;font-size:1.3rem;background:none;border:none;cursor:pointer;">⮜</button>
      <h2>Zaměstnanci</h2>
      <input class="search-box" type="text" id="searchInput" placeholder="Vyhledat jméno...">
      <ul class="employee-list" id="employeeList"></ul>
    </aside>
    <div class="mapa-wrapper">
      <div class="office-map-container">
        <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
        <!-- Markery budou generovány dynamicky -->
      </div>
    </div>
  </div>
  <div id="img-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
  <script>
    // --- MARKERY V PIXELECH ---
    let markerPositions = [
      {
        "jmeno": "Smrček Petr",
        "left": 954,
        "top": 610
      },
      {
        "jmeno": "Mičáň Alex",
        "left": 891,
        "top": 579
      },
      {
        "jmeno": "Vích Ondřej",
        "left": 1029,
        "top": 614
      },
      {
        "jmeno": "Tůma Tomáš",
        "left": 1140,
        "top": 616
      },
      {
        "jmeno": "Hrdá Veronika",
        "left": 1691,
        "top": 170
      },
      {
        "jmeno": "Vlčková Soňa",
        "left": 1689,
        "top": 211
      },
      {
        "jmeno": "Hons Jindřich",
        "left": 1607,
        "top": 170
      },
      {
        "jmeno": "Beáta Barošová",
        "left": 1563,
        "top": 169
      }
    ];

    // Pomocné funkce pro odstranění diakritiky a porovnání jmen
    function normalizeName(str) {
      return str
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .trim();
    }

    // Vytvořím pole všech avatarů z index.html (ručně vygenerované nebo importované)
    const avatarList = [
      { src: 'img/Barošová.png', alt: 'Beáta Barošová' },
      { src: 'img/Bednář.png', alt: 'Bednář Petr' },
      { src: 'img/Bočánek.jpeg', alt: 'Bočánek Stanislav' },
      { src: 'img/Boháč.jpeg', alt: 'Kryštof Boháč' },
      { src: 'img/Bok.png', alt: 'Bok Zbyněk' },
      { src: 'img/Brzobohata.png', alt: 'Jana Brzobohatá' },
      { src: 'img/Česáková.jpeg', alt: 'Česáková Andrea' },
      { src: 'img/Čermák.png', alt: 'Čermák Martin' },
      { src: 'img/Drdák.jfif', alt: 'Drdák Josef' },
      { src: 'img/Dvořák.jpeg', alt: 'Dvořák Tomáš' },
      { src: 'img/Dvořáková.jpeg', alt: 'Váchalová Zuzana' },
      { src: 'img/Erhartová.jpeg', alt: 'Erhartová Pavla' },
      { src: 'img/Fridrichová.png', alt: 'Fridrichová Katarína' },
      { src: 'img/Gabriel.png', alt: 'Gabriel Martina' },
      { src: 'img/Gregor.png', alt: 'Gregor Boris' },
      { src: 'img/Haufenhoferova.png', alt: 'Haufenhoferová Eva' },
      { src: 'img/Hesko.png', alt: 'Martin Hesko' },
      { src: 'img/Hodánek.png', alt: 'Hodánek Jaroslav' },
      { src: 'img/Hons.png', alt: 'Hons Jindřich' },
      { src: 'img/Houdek.png', alt: 'Houdek Ondřej' },
      { src: 'img/Hrdá.jpeg', alt: 'Hrdá Veronika' },
      { src: 'img/Hůlová.jpg', alt: 'Hůlová Helena' },
      { src: 'img/Chemišinec.jpeg', alt: 'Chemišinec Igor' },
      { src: 'img/Jedličková1.png', alt: 'Markéta Jedličková' },
      { src: 'img/Jahoda.png', alt: 'Jahoda Vojtěch' },
      { src: 'img/Jurásková.png', alt: 'Pavlína Jurásková' },
      { src: 'img/Kalábová.jpeg', alt: 'Kalábová Lucie' },
      { src: 'img/Karas.jpg', alt: 'Karas Karel' },
      { src: 'img/Kníže.jpeg', alt: 'Kníže Jaromír' },
      { src: 'img/Knop.png', alt: 'Knop Ondřej' },
      { src: 'img/Kohoutová.jpeg', alt: 'Kohoutová Kateřina' },
      { src: 'img/Kopecká.png', alt: 'Kopecká Zuzana' },
      { src: 'img/Kreuzman.png', alt: 'Kreuzman Jiří' },
      { src: 'img/Křivánek.jpg', alt: 'Křivánek Libor' },
      { src: 'img/Kurfiřtová.jpeg', alt: 'Kurfiřtová Pavla' },
      { src: 'img/Laco.jpeg', alt: 'Laco Dušan' },
      { src: 'img/Lebeda.jpg', alt: 'Lebeda Dušan' },
      { src: 'img/Lobotková.png', alt: 'Lobotková Alena' },
      { src: 'img/Máca.png', alt: 'Máca Ondřej' },
      { src: 'img/Mácová.jpeg', alt: 'Mácová Michaela' },
      { src: 'img/Mašková.png', alt: 'Mašková Hana' },
      { src: 'img/Marešová.jpeg', alt: 'Nardelli Magdalena' },
      { src: 'img/Mlynarčíková.jpeg', alt: 'Michaela Mlynarčíková' },
      { src: 'img/Mňuková.png', alt: 'Mňuková Kateřina' },
      { src: 'img/Mesteková.jpg', alt: 'Alice Mesteková' },
      { src: 'img/Mican.png', alt: 'Mičáň Alex' },
      { src: 'img/Nečesaný.jpeg', alt: 'Nečesaný Jakub' },
      { src: 'img/Novák.png', alt: 'Novák Petr' },
      { src: 'img/Pešková.jpeg', alt: 'Pešková Monika' },
      { src: 'img/Prihara.png', alt: 'Prihara Roman' },
      { src: 'img/Procházková.jpeg', alt: 'Procházková Kateřina' },
      { src: 'img/Puchel.jpg', alt: 'Puchel Michal' },
      { src: 'img/Ráška.png', alt: 'Raška Michal' },
      { src: 'img/Akaki.jpeg', alt: 'Sharashenidze Akaki' },
      { src: 'img/Smrček.png', alt: 'Smrček Petr' },
      { src: 'img/Sojka.png', alt: 'Sojka Alena' },
      { src: 'img/Soukupovápng.png', alt: 'Soukupová Michaela' },
      { src: 'img/Srb.jpg', alt: 'Srb Václav' },
      { src: 'img/Staňková.png', alt: 'Staňková Michaela' },
      { src: 'img/Stašková.png', alt: 'Stašková Zuzana' },
      { src: 'img/Ševčenko.jpeg', alt: 'Ševčenko Peter' },
      { src: 'img/Šrom.png', alt: 'Šrom Jakub' },
      { src: 'img/Špala.jpeg', alt: 'Špala Jaroslav' },
      { src: 'img/Tepličanec.png', alt: 'Tepličanec Pavel' },
      { src: 'img/Tomek.png', alt: 'Tomek Jiří' },
      { src: 'img/Tůma.png', alt: 'Tůma Tomáš' },
      { src: 'img/Vacek.png', alt: 'Vacek Jaroslav' },
      { src: 'img/Varvara.jpeg', alt: 'Vasjuňkina Varvara' },
      { src: 'img/Vích.jpeg', alt: 'Vích Ondřej' },
      { src: 'img/Vichrová.jpeg', alt: 'Vichrová Martina' },
      { src: 'img/Vlčková.jpeg', alt: 'Vlčková Soňa' },
      { src: 'img/Záviský.jpg', alt: 'Záviský Ondřej' }
    ];

    function getAvatarForName(jmeno) {
      const norm = normalizeName(jmeno);
      for (const av of avatarList) {
        const alt = normalizeName(av.alt);
        // Páruj podle shody obou slov (jméno i příjmení, v libovolném pořadí)
        const parts = norm.split(' ');
        if (parts.every(p => alt.includes(p))) {
          return av.src;
        }
      }
      return 'img/no-person-photo.png';
    }

    // Collapsible panel
    let panelCollapsed = false;
    function togglePanel() {
      const panel = document.querySelector('.side-panel');
      panelCollapsed = !panelCollapsed;
      panel.classList.toggle('collapsed', panelCollapsed);
      document.getElementById('collapse-btn').innerHTML = panelCollapsed ? '⮞' : '⮜';
    }

    // Načtení zaměstnanců pouze ze zamestnanci.json
    let zamestnanci = [];
    fetch('zamestnanci.json')
      .then(res => res.json())
      .then(data => {
        zamestnanci = data;
        renderEmployeeList(zamestnanci);
        renderMarkers(zamestnanci);
        enableMappingMode();
      });

    function renderEmployeeList(list) {
      const ul = document.getElementById('employeeList');
      ul.innerHTML = '';
      list.forEach(z => {
        const li = document.createElement('li');
        li.tabIndex = 0;
        li.dataset.jmeno = z.jmeno;
        // Avatar
        const avatar = document.createElement('img');
        avatar.src = getAvatarForName(z.jmeno);
        avatar.alt = z.jmeno;
        avatar.className = 'avatar-img';
        avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
        li.appendChild(avatar);
        // Jméno
        const span = document.createElement('span');
        span.textContent = z.jmeno;
        span.className = 'emp-name';
        li.appendChild(span);
        // Tlačítko najít
        const btn = document.createElement('button');
        btn.innerHTML = '<i class="fas fa-search"></i> Najít';
        btn.className = 'find-btn';
        btn.title = 'Zaměřit na mapě';
        btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
        li.appendChild(btn);
        // Celá položka klikací
        li.onclick = e => highlightByName(z.jmeno, true);
        li.addEventListener('keydown', e => {
          if (e.key === 'Enter' || e.key === ' ') {
            highlightByName(z.jmeno, true);
          }
        });
        ul.appendChild(li);
      });
    }

    function renderMarkers(list) {
      const container = document.querySelector('.office-map-container');
      // Odstranit staré markery
      container.querySelectorAll('.marker').forEach(m => m.remove());
      // Vykreslit nové markery podle markerPositions
      markerPositions.forEach((pos, i) => {
        const idx = list.findIndex(z => z.jmeno === pos.jmeno);
        if (idx === -1) return;
        const marker = document.createElement('div');
        marker.className = 'marker';
        marker.style.top = pos.top + 'px';
        marker.style.left = pos.left + 'px';
        marker.dataset.jmeno = pos.jmeno;
        marker.tabIndex = 0;
        // MODRÉ KOLEČKO místo avataru
        const blueDot = document.createElement('div');
        blueDot.style.width = '26px';
        blueDot.style.height = '26px';
        blueDot.style.borderRadius = '50%';
        blueDot.style.background = '#007bff';
        blueDot.style.boxShadow = '0 2px 8px rgba(0,123,255,0.18)';
        blueDot.style.border = '2.5px solid #fff';
        blueDot.style.display = 'block';
        marker.appendChild(blueDot);
        // Tooltip (skrytý, zobrazí se při hoveru/aktivaci)
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip';
        tooltip.innerHTML = `${pos.jmeno}<br><span class='coords'>left: ${pos.left}, top: ${pos.top}</span>`;
        marker.appendChild(tooltip);
        marker.addEventListener('click', () => highlightByName(pos.jmeno, true));
        // Drag & drop
        marker.addEventListener('mousedown', function(e) {
          e.preventDefault();
          startDragMarker(marker, i, e);
        });
        container.appendChild(marker);
      });
    }

    // Vyhledávání
    document.getElementById('searchInput').addEventListener('input', function() {
      const val = this.value.toLowerCase();
      const filtered = zamestnanci.filter(z => z.jmeno.toLowerCase().includes(val));
      renderEmployeeList(filtered);
    });

    // Zvýraznění markeru a jména, scroll na marker
    function highlightByName(jmeno, scrollTo) {
      document.querySelectorAll('.marker').forEach(marker => {
        const isActive = marker.dataset.jmeno === jmeno;
        marker.classList.toggle('active', isActive);
        if (isActive && scrollTo) {
          marker.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
        }
      });
      document.querySelectorAll('.employee-list li').forEach(li => {
        li.classList.toggle('active', li.dataset.jmeno === jmeno);
      });
    }

    // --- MAPOVACÍ REŽIM ---
    let mapped = [...markerPositions];
    function enableMappingMode() {
      const img = document.getElementById('office-map-img');
      img.style.cursor = 'crosshair';
      img.addEventListener('click', onMapClick);
    }
    function onMapClick(e) {
      const img = e.target;
      const rect = img.getBoundingClientRect();
      const left = Math.round(e.clientX - rect.left);
      const top = Math.round(e.clientY - rect.top);
      showMappingDialog(left, top);
    }
    function showMappingDialog(left, top) {
      // Dialog s výběrem jména
      const dialog = document.createElement('div');
      dialog.className = 'mapping-dialog';
      dialog.innerHTML = `
        <label for="mapping-select">Vyberte zaměstnance:</label>
        <select id="mapping-select">
          <option value="">-- Vyberte --</option>
          ${zamestnanci.map(z => `<option value="${z.jmeno}">${z.jmeno}</option>`).join('')}
        </select>
        <div>Souřadnice: <b>left: ${left}, top: ${top}</b></div>
        <button id="mapping-confirm">Přidat</button>
        <button id="mapping-cancel" style="background:#eee;color:#333;">Zrušit</button>
      `;
      document.body.appendChild(dialog);
      dialog.querySelector('#mapping-cancel').onclick = () => dialog.remove();
      dialog.querySelector('#mapping-confirm').onclick = () => {
        const jmeno = dialog.querySelector('#mapping-select').value;
        if (!jmeno) return;
        mapped.push({ jmeno, left, top });
        console.log('Aktuální pole markerPositions:', JSON.stringify(mapped, null, 2));
        dialog.remove();
        // Přidat marker na mapu s animací
        markerPositions = mapped;
        renderMarkers(zamestnanci);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => {
            if (m.dataset.jmeno === jmeno) m.classList.add('added');
          });
        }, 50);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => m.classList.remove('added'));
        }, 800);
      };
    }

    // Kontrola načtení obrázku
    const img = document.getElementById('office-map-img');
    img.onerror = function() {
      document.querySelector('.office-map-container').style.display = 'none';
      document.getElementById('img-error').style.display = 'block';
    };
    img.onload = function() {
      document.getElementById('img-error').style.display = 'none';
      document.querySelector('.office-map-container').style.display = 'inline-block';
      renderMarkers(zamestnanci);
    };

    // Drag & drop logika
    let dragMarker = null;
    let dragIdx = null;
    let dragOffsetX = 0;
    let dragOffsetY = 0;
    function startDragMarker(marker, idx, e) {
      dragMarker = marker;
      dragIdx = idx;
      const rect = marker.getBoundingClientRect();
      dragOffsetX = e.clientX - rect.left;
      dragOffsetY = e.clientY - rect.top;
      document.addEventListener('mousemove', onDragMarker);
      document.addEventListener('mouseup', stopDragMarker);
      marker.classList.add('active');
      showTooltip(marker, markerPositions[idx].left, markerPositions[idx].top, true);
    }
    function onDragMarker(e) {
      if (!dragMarker) return;
      const container = document.querySelector('.office-map-container');
      const crect = container.getBoundingClientRect();
      let left = Math.round(e.clientX - crect.left - dragOffsetX + 13); // +13 pro střed markeru
      let top = Math.round(e.clientY - crect.top - dragOffsetY + 13);
      // Omezit na hranice obrázku
      left = Math.max(0, Math.min(left, container.offsetWidth - 26));
      top = Math.max(0, Math.min(top, container.offsetHeight - 26));
      dragMarker.style.left = left + 'px';
      dragMarker.style.top = top + 'px';
      showTooltip(dragMarker, left, top, true);
    }
    function stopDragMarker(e) {
      if (!dragMarker) return;
      const left = parseInt(dragMarker.style.left);
      const top = parseInt(dragMarker.style.top);
      markerPositions[dragIdx].left = left;
      markerPositions[dragIdx].top = top;
      dragMarker.classList.remove('active');
      showTooltip(dragMarker, left, top, false);
      dragMarker = null;
      dragIdx = null;
      document.removeEventListener('mousemove', onDragMarker);
      document.removeEventListener('mouseup', stopDragMarker);
      // Vypiš nové pole do konzole
      console.log('Aktuální pole markerPositions:', JSON.stringify(markerPositions, null, 2));
    }
    function showTooltip(marker, left, top, dragging) {
      const tooltip = marker.querySelector('.tooltip');
      tooltip.innerHTML = `${marker.dataset.jmeno}<br><span class='coords'>left: ${left}, top: ${top}${dragging ? ' <b>(přesouváš)</b>' : ''}</span>`;
      tooltip.style.opacity = 1;
    }

    // Fallback pro chybějící obrázky
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.avatar-img').forEach(img => {
        img.onerror = () => { img.src = 'img/no-person-photo.png'; };
      });
    });
  </script>
</body>
</html> 